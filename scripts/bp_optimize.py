#!/usr/bin/env python3
"""
Best Practice Optuna Optimization Script
Реализует правильную логику zscore с гистерезисом и анти-чурн штрафами
"""

import argparse
import yaml
import optuna
import numpy as np
import random
import json
import logging
from pathlib import Path
from datetime import datetime
import sys
import os

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "src"))

# Импортируем BP objective функцию
from bp_objective import create_bp_objective_function


def setup_logging(study_name: str) -> logging.Logger:
    """Настройка логирования"""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    log_file = logs_dir / f"{study_name}_bp_optimization.log"
    
    logger = logging.getLogger(f"bp_optuna_{study_name}")
    logger.setLevel(logging.INFO)
    
    # Удаляем существующие handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter('%(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    return logger


def load_search_space(space_file: str) -> dict:
    """Загрузка пространства поиска"""
    with open(space_file, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def set_seeds(seed: int = 42):
    """Установка seeds для воспроизводимости"""
    random.seed(seed)
    np.random.seed(seed)
    # Если используется torch/tensorflow, добавить их seeds


def create_objective_function(base_config: str, search_space: dict, logger: logging.Logger):
    """Создание objective функции с best practices"""

    # Используем BP objective функцию
    return create_bp_objective_function(base_config, search_space, logger)



def main():
    parser = argparse.ArgumentParser(description='Best Practice Optuna Optimization')
    parser.add_argument('--base', required=True, help='Базовый конфиг (main_2024.yaml)')
    parser.add_argument('--space', required=True, help='Пространство поиска')
    parser.add_argument('--trials', type=int, default=400, help='Количество trials')
    parser.add_argument('--study', required=True, help='Название study')
    parser.add_argument('--storage', required=True, help='Storage URL (sqlite:///studies.db)')
    parser.add_argument('--n-jobs', type=int, default=1, help='Количество параллельных процессов')
    
    args = parser.parse_args()
    
    # Установка seeds
    set_seeds(42)
    
    # Настройка логирования
    logger = setup_logging(args.study)
    
    logger.info("="*80)
    logger.info(f"НАЧАЛО BEST PRACTICE ОПТИМИЗАЦИИ: {args.study}")
    logger.info(f"Базовый конфиг: {args.base}")
    logger.info(f"Пространство поиска: {args.space}")
    logger.info(f"Количество trials: {args.trials}")
    logger.info(f"Storage: {args.storage}")
    logger.info("="*80)
    
    # Загрузка пространства поиска
    search_space = load_search_space(args.space)
    
    # Создание objective функции
    objective_func = create_objective_function(args.base, search_space, logger)
    
    # Настройка Optuna
    study = optuna.create_study(
        study_name=args.study,
        storage=args.storage,
        direction="maximize",
        load_if_exists=True,
        sampler=optuna.samplers.TPESampler(
            seed=42,
            n_startup_trials=50,
            multivariate=True
        ),
        pruner=optuna.pruners.MedianPruner(
            n_warmup_steps=30,
            interval_steps=1
        )
    )
    
    # Запуск оптимизации
    logger.info(f"Запуск оптимизации на {args.trials} trials...")
    study.optimize(objective_func, n_trials=args.trials, n_jobs=args.n_jobs)
    
    # Результаты
    logger.info("\n" + "="*80)
    logger.info("РЕЗУЛЬТАТЫ ОПТИМИЗАЦИИ")
    logger.info("="*80)
    
    if study.best_trial:
        logger.info(f"Лучший результат: {study.best_trial.value:.6f}")
        logger.info(f"Лучшие параметры:")
        for key, value in study.best_trial.params.items():
            logger.info(f"  {key}: {value}")
        
        # Сохранение лучших параметров
        best_params_file = f"best_params_{args.study}.json"
        with open(best_params_file, 'w', encoding='utf-8') as f:
            json.dump({
                'study_name': args.study,
                'best_value': study.best_trial.value,
                'best_params': study.best_trial.params,
                'timestamp': str(datetime.now())
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Лучшие параметры сохранены: {best_params_file}")
    
    logger.info(f"Завершено trials: {len(study.trials)}")
    logger.info("Оптимизация завершена!")


if __name__ == "__main__":
    main()
