#!/usr/bin/env python3

import sys
import time

# Записываем в файл для проверки что процесс работает
with open('test_output.log', 'w') as f:
    f.write("ТЕСТ НАЧАЛСЯ\n")
    f.flush()

print("Тест начался")

sys.path.append('src')

with open('test_output.log', 'a') as f:
    f.write("Путь добавлен\n")
    f.flush()

try:
    from optimiser.fast_objective import FastWalkForwardObjective
    
    with open('test_output.log', 'a') as f:
        f.write("Импорт успешен\n")
        f.flush()
    
    # Создаем objective
    obj = FastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    
    with open('test_output.log', 'a') as f:
        f.write(f"Objective создан, пар: {len(obj.preselected_pairs)}\n")
        f.flush()
    
    print(f"Objective создан, пар: {len(obj.preselected_pairs)}")
    
except Exception as e:
    with open('test_output.log', 'a') as f:
        f.write(f"ОШИБКА: {e}\n")
        f.flush()
    
    print(f"Ошибка: {e}")

with open('test_output.log', 'a') as f:
    f.write("ТЕСТ ЗАВЕРШЕН\n")
    f.flush()

print("Тест завершен")
