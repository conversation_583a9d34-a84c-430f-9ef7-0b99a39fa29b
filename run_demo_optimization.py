#!/usr/bin/env python3
"""
Демонстрационная оптимизация с ограниченным количеством пар
"""

import sys
from pathlib import Path
import pandas as pd
import optuna
import logging
import time

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DemoObjective(FastWalkForwardObjective):
    """Демонстрационная версия с ограниченным количеством пар"""
    
    def __init__(self, base_config_path, search_space_path, max_pairs=20):
        super().__init__(base_config_path, search_space_path)
        
        # Ограичиваем количество пар для демонстрации
        original_count = len(self.preselected_pairs)
        self.preselected_pairs = self.preselected_pairs.head(max_pairs)
        
        logger.info(f"🎯 ДЕМО РЕЖИМ: {original_count} → {len(self.preselected_pairs)} пар")


def run_demo_optimization():
    """Запуск демонстрационной оптимизации"""
    
    logger.info("🚀 ДЕМОНСТРАЦИОННАЯ ОПТИМИЗАЦИЯ")
    logger.info("="*50)
    logger.info("Цель: Показать что все исправления работают")
    
    # Создаем objective с ограниченным количеством пар
    objective = DemoObjective(
        'configs/main_2024.yaml', 
        'configs/search_space_fast.yaml',
        max_pairs=20  # Только 20 пар для демонстрации
    )
    
    # Создаем study с оптимальными настройками
    study = optuna.create_study(
        direction="maximize",
        study_name="demo_fixed_optimization",
        sampler=optuna.samplers.TPESampler(
            seed=42,
            multivariate=True,
            group=True,
            n_startup_trials=2
        ),
        pruner=optuna.pruners.MedianPruner(
            n_warmup_steps=1,
            interval_steps=1
        )
    )
    
    logger.info(f"📊 Study создан: {study.study_name}")
    
    # Добавляем базовую точку для TPE
    base_params = {
        'zscore_threshold': 1.0,
        'zscore_exit': 0.3,
        'rolling_window': 25,
        'max_active_positions': 15,
        'risk_per_position_pct': 0.02,
        'max_position_size_pct': 0.1,
        'stop_loss_multiplier': 3.0,
        'time_stop_multiplier': 5.0,
        'cooldown_hours': 2,
        'commission_pct': 0.0004,
        'slippage_pct': 0.0005,
        'normalization_method': 'minmax',
        'min_history_ratio': 0.6
    }
    study.enqueue_trial(base_params)
    logger.info("🎯 Базовая точка добавлена для TPE")
    
    def objective_with_logging(trial):
        """Objective функция с детальным логированием"""
        start_time = time.time()
        logger.info(f"\n🔄 Trial {trial.number} начался")
        
        try:
            result = objective(trial)
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"✅ Trial {trial.number} завершен: {result:.6f} (время: {duration:.1f}с)")
            
            # Получаем метрики из user_attrs если они есть
            if hasattr(trial, 'user_attrs') and 'metrics' in trial.user_attrs:
                metrics = trial.user_attrs['metrics']
                trades = metrics.get('total_trades', 0)
                sharpe = metrics.get('sharpe', 0)
                max_dd = metrics.get('max_drawdown', 0)
                pos_days = metrics.get('positive_days_rate', 0)
                
                logger.info(f"   📊 Детали: {trades} сделок, Sharpe: {sharpe:.4f}, DD: {max_dd:.4f}, Pos.days: {pos_days:.4f}")
                
                # Проверяем успешность
                if trades > 0:
                    logger.info(f"   🎉 ЕСТЬ СДЕЛКИ! Исправление работает!")
                else:
                    logger.warning(f"   ⚠️ Нет сделок в этом trial")
            
            return result
            
        except optuna.TrialPruned as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"✂️ Trial {trial.number} pruned: {e} (время: {duration:.1f}с)")
            raise
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"❌ Trial {trial.number} ошибка: {e} (время: {duration:.1f}с)")
            import traceback
            traceback.print_exc()
            return -1000.0
    
    # Запускаем оптимизацию
    n_trials = 3
    logger.info(f"⚡ Запуск {n_trials} trials...")
    
    try:
        study.optimize(objective_with_logging, n_trials=n_trials)
        
        # Анализ результатов
        logger.info(f"\n📊 РЕЗУЛЬТАТЫ ДЕМОНСТРАЦИИ:")
        logger.info(f"   Всего trials: {len(study.trials)}")
        
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        pruned_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]
        failed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.FAIL]
        
        logger.info(f"   Завершенных: {len(completed_trials)}")
        logger.info(f"   Pruned: {len(pruned_trials)}")
        logger.info(f"   Ошибок: {len(failed_trials)}")
        
        # Анализ успешности исправлений
        successful_trials = 0
        trials_with_trades = 0
        
        for trial in completed_trials:
            if trial.value > -5:
                successful_trials += 1
            
            if 'metrics' in trial.user_attrs:
                metrics = trial.user_attrs['metrics']
                if metrics.get('total_trades', 0) > 0:
                    trials_with_trades += 1
        
        logger.info(f"\n🎯 АНАЛИЗ ИСПРАВЛЕНИЙ:")
        logger.info(f"   Trials с результатом > -5: {successful_trials}/{len(completed_trials)}")
        logger.info(f"   Trials со сделками: {trials_with_trades}/{len(completed_trials)}")
        
        if study.best_trial:
            logger.info(f"\n🏆 ЛУЧШИЙ РЕЗУЛЬТАТ:")
            logger.info(f"   Значение: {study.best_value:.6f}")
            logger.info(f"   Параметры: {study.best_params}")
            
            if 'metrics' in study.best_trial.user_attrs:
                metrics = study.best_trial.user_attrs['metrics']
                logger.info(f"   📊 Метрики:")
                logger.info(f"      Сделок: {metrics.get('total_trades', 0)}")
                logger.info(f"      Sharpe: {metrics.get('sharpe', 0):.4f}")
                logger.info(f"      Max DD: {metrics.get('max_drawdown', 0):.4f}")
                logger.info(f"      Positive days: {metrics.get('positive_days_rate', 0):.4f}")
        
        # Итоговая оценка
        if successful_trials > 0 and trials_with_trades > 0:
            logger.info(f"\n🎉 ДЕМОНСТРАЦИЯ УСПЕШНА!")
            logger.info(f"   ✅ Исправления работают корректно")
            logger.info(f"   ✅ Проблема с 0 сделок решена")
            logger.info(f"   ✅ Оптимизация готова к продакшену")
            return True
        else:
            logger.warning(f"\n⚠️ ПРОБЛЕМЫ В ДЕМОНСТРАЦИИ")
            logger.warning(f"   Нужна дополнительная диагностика")
            return False
            
    except Exception as e:
        logger.error(f"\n❌ Ошибка демонстрации: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Главная функция"""
    try:
        success = run_demo_optimization()
        
        if success:
            logger.info(f"\n✅ ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
            logger.info(f"   Все исправления работают")
            logger.info(f"   Можно запускать полную оптимизацию")
        else:
            logger.error(f"\n❌ ПРОБЛЕМЫ В ДЕМОНСТРАЦИИ")
            
        return success
        
    except Exception as e:
        logger.error(f"\n💥 КРИТИЧЕСКАЯ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
