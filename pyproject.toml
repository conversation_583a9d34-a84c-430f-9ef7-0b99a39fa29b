[tool.poetry]
name = "coint2" # Оставляем оригинальное имя проекта
version = "0.1.0"
description = "Cointegration backtest framework"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[tool.poetry.dependencies]
python = "^3.10"
click = "*"
joblib = "*"
pydantic = "*"
pyyaml = "^6.0"
numpy = "*"
pandas = "*"
scipy = "^1.13.0"
numba = "*"
# ↓↓↓ ЭТИ ЗАВИСИМОСТИ МЫ БЕРЕМ ИЗ ВЕТКИ CODEX ↓↓↓
pyarrow = ">=8.0.0"
statsmodels = "*"
dask = {extras = ["dataframe"], version = "*"} # Устанавливаем dask с поддержкой DataFrame
dask-expr = "*" # Нужен для read_parquet в новых версиях
python-dotenv = "*"
matplotlib = "*"  # Для графиков
seaborn = "*"     # Для красивых графиков
plotly = "*"      # Для интерактивных графиков
polars = "^1.31.0"
tqdm = "^4.67.1"
hurst = "*"
psutil = "*"
scikit-learn = "*"

# Moved to tool.poetry.group.dev.dependencies
optuna = "^4.4.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4"
mypy = "^1.10"
pytest = "*"

[tool.poetry.scripts]
coint2 = "coint2.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true

[tool.ruff]
line-length = 120
select = ["E", "F", "B", "I", "UP"]

