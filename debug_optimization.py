#!/usr/bin/env python3
"""
Диагностический тест оптимизации
"""

import sys
from pathlib import Path
import optuna

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective


def main():
    """Простой тест оптимизации с одним trial"""
    
    print("🔍 ДИАГНОСТИЧЕСКИЙ ТЕСТ ОПТИМИЗАЦИИ")
    print("="*50)
    
    # Создаем objective
    objective = FastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    
    print(f"🔍 ТЕСТИРУЕМ ОПТИМИЗАЦИЮ")
    print(f"   Период тренировки: {objective.base_config.walk_forward.training_period_days} дней")
    print(f"   Период тестирования: {objective.base_config.walk_forward.testing_period_days} дней")
    print(f"   Начало: {objective.base_config.walk_forward.start_date}")
    print(f"   Конец: {objective.base_config.walk_forward.end_date}")
    
    # Создаем study
    study = optuna.create_study(direction="maximize")
    
    print(f"\n🚀 Запуск одного trial...")
    
    def test_objective(trial):
        """Тестовая objective функция с логированием"""
        print(f"\n📊 TRIAL ПАРАМЕТРЫ:")
        
        # Генерируем параметры
        params = {}
        
        # Сэмплируем zscore_threshold
        z_threshold = trial.suggest_float("zscore_threshold", 0.5, 1.5)
        params['zscore_threshold'] = z_threshold
        print(f"   zscore_threshold: {z_threshold}")
        
        # Сэмплируем zscore_exit
        z_exit = trial.suggest_float("zscore_exit", 0.1, min(0.8, z_threshold - 0.1))
        params['zscore_exit'] = z_exit
        print(f"   zscore_exit: {z_exit}")
        
        # Сэмплируем rolling_window
        rolling_window = trial.suggest_int("rolling_window", 15, 30, step=5)
        params['rolling_window'] = rolling_window
        print(f"   rolling_window: {rolling_window}")
        
        # Добавляем остальные параметры
        params.update({
            'max_active_positions': 20,
            'risk_per_position_pct': 0.02,
            'max_position_size_pct': 0.15,
            'stop_loss_multiplier': 5.0,
            'time_stop_multiplier': 5.0,
            'cooldown_hours': 1,
            'commission_pct': 0.0004,
            'slippage_pct': 0.0005,
            'normalization_method': 'minmax',
            'min_history_ratio': 0.6,
            'trial_number': trial.number,
        })
        
        print(f"\n🔄 Вызываем objective функцию...")
        
        try:
            result = objective(params)
            print(f"\n✅ РЕЗУЛЬТАТ: {result:.6f}")
            return result
        except Exception as e:
            print(f"\n❌ ОШИБКА: {e}")
            import traceback
            traceback.print_exc()
            return -1000.0
    
    # Запускаем один trial
    study.optimize(test_objective, n_trials=1)
    
    print(f"\n📊 ИТОГИ:")
    print(f"   Лучший результат: {study.best_value}")
    print(f"   Лучшие параметры: {study.best_params}")


if __name__ == "__main__":
    main()
