#!/usr/bin/env python3
"""
Строгие тесты для проверки корректности расчета торговых метрик.

Проверяет исправление критических ошибок:
1. Win rate считается по сделкам, а не по барам
2. Средний размер сделки считается по сделкам, а не по барам  
3. Средний hold-time считается корректно
4. Позитивные дни считаются по дням, а не по барам
5. Правильная обработка 15-минутных данных для дневных метрик
"""

import sys
import unittest
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Добавляем src в путь
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective
from coint2.utils.config import load_config


class TestTradeMetricsCorrectness(unittest.TestCase):
    """Строгие тесты корректности торговых метрик."""
    
    def setUp(self):
        """Подготовка тестовых данных."""
        # Создаем синтетические 15-минутные данные на 3 дня
        start_date = pd.Timestamp('2023-08-01 00:00:00')
        # 15-минутные интервалы: 4 * 24 = 96 баров в день, 3 дня = 288 баров
        periods = 288
        
        self.index = pd.date_range(start=start_date, periods=periods, freq='15min')
        
        # Создаем синтетические PnL данные с известными характеристиками
        # Паттерн: 2 прибыльные сделки, 1 убыточная, повторяется
        pnl_pattern = []
        
        # Сделка 1: прибыльная, длится 8 баров (2 часа)
        pnl_pattern.extend([0, 0, 10, 5, 3, 2, 0, 0])  # 8 баров, PnL = 20
        
        # Сделка 2: убыточная, длится 4 бара (1 час)  
        pnl_pattern.extend([0, -5, -3, 0])  # 4 бара, PnL = -8
        
        # Сделка 3: прибыльная, длится 12 баров (3 часа)
        pnl_pattern.extend([0, 0, 15, 8, 4, 3, 2, 1, 1, 1, 0, 0])  # 12 бара, PnL = 35
        
        # Повторяем паттерн для заполнения всех 288 баров
        full_pattern = []
        pattern_length = len(pnl_pattern)  # 24 бара = 6 часов
        
        for i in range(periods):
            full_pattern.append(pnl_pattern[i % pattern_length])
        
        self.pnl_series = pd.Series(full_pattern, index=self.index)
        
        # Ожидаемые результаты для проверки
        # В каждом паттерне (24 бара): 3 сделки, 2 прибыльные, 1 убыточная
        # За 3 дня (288 баров): 12 паттернов = 36 сделок, 24 прибыльные, 12 убыточных
        self.expected_total_trades = 36
        self.expected_win_rate = 24 / 36  # 2/3 = 0.6667
        self.expected_avg_trade_pnl = (20 - 8 + 35) / 3  # 47/3 = 15.667 за паттерн
        self.expected_avg_hold_time = (8 + 4 + 12) / 3  # 24/3 = 8 баров за паттерн
        
        # Дневные PnL: каждый день содержит 4 паттерна = 4 * 47 = 188 PnL
        # День 1: 188, День 2: 188, День 3: 188 - все положительные
        self.expected_positive_days_rate = 1.0  # 3/3 = 100%
        
    def test_win_rate_calculated_by_trades_not_bars(self):
        """Тест: Win rate должен считаться по сделкам, а не по барам."""
        
        # Создаем фиктивную позицию на основе PnL
        position = (self.pnl_series != 0).astype(int)
        
        # Находим сделки
        trade_start = (position.shift(fill_value=0) == 0) & (position != 0)
        trade_id = trade_start.cumsum()
        trade_id = trade_id.where(position != 0, None)
        
        # PnL по сделкам
        trade_pnls = self.pnl_series.groupby(trade_id).sum().dropna()
        
        # Проверяем количество сделок
        actual_trades = len(trade_pnls)
        self.assertEqual(actual_trades, self.expected_total_trades, 
                        f"Ожидалось {self.expected_total_trades} сделок, получено {actual_trades}")
        
        # Проверяем win rate по сделкам
        actual_win_rate = (trade_pnls > 0).mean()
        self.assertAlmostEqual(actual_win_rate, self.expected_win_rate, places=4,
                              msg=f"Win rate по сделкам: ожидался {self.expected_win_rate:.4f}, получен {actual_win_rate:.4f}")
        
        # Проверяем, что win rate по барам ОТЛИЧАЕТСЯ (доказывает исправление)
        bars_win_rate = (self.pnl_series > 0).mean()
        self.assertNotAlmostEqual(bars_win_rate, actual_win_rate, places=2,
                                 msg="Win rate по барам не должен равняться win rate по сделкам")
        
    def test_avg_trade_size_calculated_by_trades_not_bars(self):
        """Тест: Средний размер сделки должен считаться по сделкам, а не по барам."""
        
        # Создаем фиктивную позицию на основе PnL
        position = (self.pnl_series != 0).astype(int)
        
        # Находим сделки
        trade_start = (position.shift(fill_value=0) == 0) & (position != 0)
        trade_id = trade_start.cumsum()
        trade_id = trade_id.where(position != 0, None)
        
        # PnL по сделкам
        trade_pnls = self.pnl_series.groupby(trade_id).sum().dropna()
        
        # Средний размер сделки по сделкам
        actual_avg_trade_size = trade_pnls.abs().mean()
        
        # Проверяем корректность
        expected_pattern_avg = abs(20) + abs(-8) + abs(35)  # 63
        expected_avg_trade_size = expected_pattern_avg / 3  # 21.0
        
        self.assertAlmostEqual(actual_avg_trade_size, expected_avg_trade_size, places=2,
                              msg=f"Средний размер сделки: ожидался {expected_avg_trade_size:.2f}, получен {actual_avg_trade_size:.2f}")
        
        # Проверяем, что средний размер по барам ОТЛИЧАЕТСЯ
        bars_avg_size = self.pnl_series.abs().mean()
        self.assertNotAlmostEqual(bars_avg_size, actual_avg_trade_size, places=1,
                                 msg="Средний размер по барам не должен равняться среднему размеру по сделкам")
        
    def test_avg_hold_time_calculated_correctly(self):
        """Тест: Средний hold-time должен считаться корректно."""
        
        # Создаем фиктивную позицию на основе PnL
        position = (self.pnl_series != 0).astype(int)
        
        # Находим сделки
        trade_start = (position.shift(fill_value=0) == 0) & (position != 0)
        trade_id = trade_start.cumsum()
        trade_id = trade_id.where(position != 0, None)
        
        # Длительность сделок в барах
        hold_bars = position.groupby(trade_id).sum().dropna()
        
        # Средний hold-time
        actual_avg_hold_time = hold_bars.mean()
        
        self.assertAlmostEqual(actual_avg_hold_time, self.expected_avg_hold_time, places=2,
                              msg=f"Средний hold-time: ожидался {self.expected_avg_hold_time:.2f} баров, получен {actual_avg_hold_time:.2f}")
        
    def test_positive_days_calculated_by_days_not_bars(self):
        """Тест: Позитивные дни должны считаться по дням, а не по барам."""
        
        # Агрегируем PnL по дням
        daily_pnl = self.pnl_series.resample('1D').sum()
        
        # Проверяем количество дней
        self.assertEqual(len(daily_pnl), 3, "Должно быть 3 дня данных")
        
        # Проверяем, что все дни положительные
        positive_days_rate = (daily_pnl > 0).mean()
        
        self.assertAlmostEqual(positive_days_rate, self.expected_positive_days_rate, places=4,
                              msg=f"Доля позитивных дней: ожидалась {self.expected_positive_days_rate:.4f}, получена {positive_days_rate:.4f}")
        
        # Проверяем, что доля позитивных баров ОТЛИЧАЕТСЯ
        positive_bars_rate = (self.pnl_series > 0).mean()
        self.assertNotAlmostEqual(positive_bars_rate, positive_days_rate, places=2,
                                 msg="Доля позитивных баров не должна равняться доле позитивных дней")
        
    def test_daily_returns_resampling_for_15min_data(self):
        """Тест: Правильная обработка 15-минутных данных для дневных доходностей."""
        
        # Создаем equity curve
        initial_capital = 100000
        equity_curve = initial_capital + self.pnl_series.cumsum()
        
        # Правильный способ: ресемплинг к дневным данным
        daily_equity = equity_curve.resample('1D').last()
        daily_returns = daily_equity.pct_change().dropna()
        
        # Неправильный способ: прямой расчет доходностей по 15-минутным данным
        wrong_returns = equity_curve.pct_change().dropna()
        
        # Проверяем, что количество дневных доходностей корректно
        self.assertEqual(len(daily_returns), 2, "Должно быть 2 дневных доходности (3 дня - 1)")
        
        # Проверяем, что дневные и 15-минутные доходности ОТЛИЧАЮТСЯ
        self.assertNotEqual(len(daily_returns), len(wrong_returns),
                           "Количество дневных и 15-минутных доходностей должно отличаться")
        
        # Проверяем разумность дневных доходностей
        for ret in daily_returns:
            self.assertGreater(ret, -1.0, "Дневная доходность не может быть меньше -100%")
            self.assertLess(ret, 10.0, "Дневная доходность не должна превышать 1000%")


if __name__ == '__main__':
    unittest.main()
