#!/usr/bin/env python3
"""
Тест исправлений оптимизации Optuna
"""

import sys
from pathlib import Path
import tempfile
import os

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.run_optimization import _fmt4, _convert_numpy_types
import numpy as np


def test_fmt4_function():
    """Тест безопасного форматирования"""
    print("🧪 Тестируем _fmt4...")
    
    # Тест с числами
    assert _fmt4(3.14159) == "3.1416"
    assert _fmt4(42) == "42.0000"
    
    # Тест с не-числами
    assert _fmt4("N/A") == "N/A"
    assert _fmt4(None) == "None"
    
    print("✅ _fmt4 работает корректно")


def test_numpy_conversion():
    """Тест преобразования numpy типов"""
    print("🧪 Тестируем _convert_numpy_types...")
    
    # Тест с numpy типами
    test_data = {
        'int_val': np.int64(42),
        'float_val': np.float64(3.14),
        'array_val': np.array([1, 2, 3]),
        'nested': {
            'numpy_int': np.int32(100),
            'regular_str': "test"
        },
        'list_with_numpy': [np.float32(1.5), "string", np.int16(7)]
    }
    
    converted = _convert_numpy_types(test_data)
    
    # Проверяем типы
    assert isinstance(converted['int_val'], int)
    assert isinstance(converted['float_val'], float)
    assert isinstance(converted['array_val'], list)
    assert isinstance(converted['nested']['numpy_int'], int)
    assert isinstance(converted['list_with_numpy'][0], float)
    assert isinstance(converted['list_with_numpy'][2], int)
    
    # Проверяем значения
    assert converted['int_val'] == 42
    assert abs(converted['float_val'] - 3.14) < 1e-10
    assert converted['array_val'] == [1, 2, 3]
    
    print("✅ _convert_numpy_types работает корректно")


def test_optimization_parameters():
    """Тест параметров оптимизации"""
    print("🧪 Тестируем параметры оптимизации...")
    
    # Тест адаптивного n_startup_trials
    def calc_startup_trials(n_trials):
        return min(50, max(5, n_trials // 5))
    
    assert calc_startup_trials(10) == 5    # min case
    assert calc_startup_trials(100) == 20  # normal case
    assert calc_startup_trials(1000) == 50 # max case
    
    # Тест нормализации n_jobs
    def normalize_n_jobs(n_jobs):
        if n_jobs is None or n_jobs < 1:
            return os.cpu_count() or 1
        return n_jobs
    
    assert normalize_n_jobs(None) >= 1
    assert normalize_n_jobs(0) >= 1
    assert normalize_n_jobs(-1) >= 1
    assert normalize_n_jobs(4) == 4
    
    print("✅ Параметры оптимизации корректны")


def test_config_naming():
    """Тест уникального именования конфигов"""
    print("🧪 Тестируем именование конфигов...")
    
    from datetime import datetime
    
    study_name = "test_study"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    expected_pattern = f"configs/best_config__{study_name}__{timestamp}.yaml"
    
    # Проверяем что паттерн содержит нужные элементы
    assert "best_config__" in expected_pattern
    assert study_name in expected_pattern
    assert ".yaml" in expected_pattern
    assert len(timestamp) == 15  # YYYYMMDD_HHMMSS
    
    print("✅ Именование конфигов корректно")


def main():
    """Запуск всех тестов"""
    print("🚀 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЙ ОПТИМИЗАЦИИ")
    print("="*50)
    
    try:
        test_fmt4_function()
        test_numpy_conversion()
        test_optimization_parameters()
        test_config_naming()
        
        print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("✅ Исправления работают корректно")
        
    except Exception as e:
        print(f"\n❌ ТЕСТ ПРОВАЛЕН: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
