#!/usr/bin/env python3
"""
Детальная диагностика проблемы с 0 сделок в оптимизации
"""

import sys
from pathlib import Path
import optuna

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective


def debug_objective_call():
    """Отладка вызова objective функции"""
    
    print("🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ОПТИМИЗАЦИИ")
    print("="*50)
    
    # Создаем objective
    objective = FastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    
    print(f"✅ Objective создан успешно")
    print(f"   Предотобранных пар: {len(objective.preselected_pairs)}")
    
    # Создаем study и trial
    study = optuna.create_study(direction="maximize")
    
    print(f"\n🔧 ТЕСТ 1: Создание trial и генерация параметров")
    trial = study.ask()
    print(f"   Trial создан: {type(trial)}")
    
    # Генерируем параметры
    try:
        params = objective._suggest_parameters(trial)
        print(f"   ✅ Параметры сгенерированы: {len(params)} параметров")
        
        # Показываем ключевые параметры
        key_params = ['zscore_threshold', 'rolling_window', 'max_active_positions']
        for key in key_params:
            if key in params:
                print(f"      {key}: {params[key]}")
                
    except Exception as e:
        print(f"   ❌ Ошибка генерации параметров: {e}")
        return False
    
    print(f"\n🔧 ТЕСТ 2: Прямой вызов с параметрами (как в диагностических тестах)")
    try:
        # Используем те же параметры что сгенерировал trial
        result_dict = objective(params)
        print(f"   ✅ Результат с dict: {result_dict}")
        
        if result_dict > -5:
            print(f"   🎉 С dict параметрами ЕСТЬ результат!")
        else:
            print(f"   ❌ С dict параметрами тоже проблема")
            
    except Exception as e:
        print(f"   ❌ Ошибка с dict: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🔧 ТЕСТ 3: Вызов с trial объектом (как в реальной оптимизации)")
    try:
        # Создаем новый trial для чистого теста
        trial2 = study.ask()
        result_trial = objective(trial2)
        print(f"   ✅ Результат с trial: {result_trial}")
        
        if result_trial > -5:
            print(f"   🎉 С trial объектом ЕСТЬ результат!")
        else:
            print(f"   ❌ С trial объектом проблема")
            
    except Exception as e:
        print(f"   ❌ Ошибка с trial: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🔧 ТЕСТ 4: Проверка различий в обработке")
    
    # Проверяем как определяется тип входного параметра
    print(f"   isinstance(params, dict): {isinstance(params, dict)}")
    print(f"   hasattr(trial, 'suggest_float'): {hasattr(trial, 'suggest_float')}")
    print(f"   hasattr(params, 'suggest_float'): {hasattr(params, 'suggest_float')}")
    
    return True


def debug_minimal_optimization():
    """Минимальная оптимизация для отладки"""
    
    print(f"\n🚀 МИНИМАЛЬНАЯ ОПТИМИЗАЦИЯ")
    print("="*30)
    
    # Создаем objective
    objective = FastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    
    # Создаем study
    study = optuna.create_study(direction="maximize")
    
    print(f"📊 Запускаем 1 trial...")
    
    def debug_objective(trial):
        print(f"\n   🔄 Trial {trial.number} начался")
        print(f"      Trial type: {type(trial)}")
        
        try:
            result = objective(trial)
            print(f"      ✅ Trial {trial.number} завершен: {result}")
            return result
        except Exception as e:
            print(f"      ❌ Trial {trial.number} ошибка: {e}")
            import traceback
            traceback.print_exc()
            return -1000.0
    
    # Запускаем 1 trial
    study.optimize(debug_objective, n_trials=1)
    
    print(f"\n📊 РЕЗУЛЬТАТЫ:")
    print(f"   Trials: {len(study.trials)}")
    if study.trials:
        trial = study.trials[0]
        print(f"   Статус: {trial.state}")
        print(f"   Значение: {trial.value}")
        print(f"   Параметры: {len(trial.params) if trial.params else 0}")


def main():
    """Главная функция диагностики"""
    
    try:
        # Тест 1: Детальная диагностика вызовов
        if not debug_objective_call():
            return False
        
        # Тест 2: Минимальная оптимизация
        debug_minimal_optimization()
        
        print(f"\n🎯 ДИАГНОСТИКА ЗАВЕРШЕНА")
        return True
        
    except Exception as e:
        print(f"\n❌ КРИТИЧЕСКАЯ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
