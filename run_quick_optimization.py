#!/usr/bin/env python3
"""
Быстрая оптимизация с ограниченным количеством пар для тестирования исправлений
"""

import sys
from pathlib import Path
import pandas as pd
import optuna
import logging

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QuickTestObjective(FastWalkForwardObjective):
    """Быстрая тестовая версия с ограниченным количеством пар"""
    
    def __init__(self, base_config_path, search_space_path, max_pairs=50):
        super().__init__(base_config_path, search_space_path)
        
        # Ограничиваем количество пар для быстрого тестирования
        original_count = len(self.preselected_pairs)
        self.preselected_pairs = self.preselected_pairs.head(max_pairs)
        
        logger.info(f"🔧 Ограничено количество пар: {original_count} → {len(self.preselected_pairs)}")


def run_quick_optimization():
    """Запуск быстрой оптимизации"""
    
    logger.info("🚀 ЗАПУСК БЫСТРОЙ ОПТИМИЗАЦИИ")
    logger.info("="*50)
    
    # Создаем objective с ограниченным количеством пар
    objective = QuickTestObjective(
        'configs/main_2024.yaml', 
        'configs/search_space_fast.yaml',
        max_pairs=50  # Только 50 пар для быстрого теста
    )
    
    # Создаем study
    study = optuna.create_study(
        direction="maximize",
        study_name="quick_test_fixed",
        sampler=optuna.samplers.TPESampler(
            seed=42,
            multivariate=True,
            n_startup_trials=2  # Меньше startup trials для быстрого теста
        ),
        pruner=optuna.pruners.MedianPruner(
            n_warmup_steps=1,
            interval_steps=1
        )
    )
    
    logger.info(f"📊 Study создан: {study.study_name}")
    
    def objective_with_logging(trial):
        """Objective функция с детальным логированием"""
        logger.info(f"\n🔄 Trial {trial.number} начался")
        
        try:
            result = objective(trial)
            logger.info(f"✅ Trial {trial.number} завершен: {result:.6f}")
            
            # Получаем метрики из user_attrs если они есть
            if hasattr(trial, 'user_attrs') and 'metrics' in trial.user_attrs:
                metrics = trial.user_attrs['metrics']
                trades = metrics.get('total_trades', 0)
                sharpe = metrics.get('sharpe', 0)
                logger.info(f"   📊 Сделок: {trades}, Sharpe: {sharpe:.4f}")
            
            return result
            
        except optuna.TrialPruned as e:
            logger.info(f"✂️ Trial {trial.number} pruned: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ Trial {trial.number} ошибка: {e}")
            import traceback
            traceback.print_exc()
            return -1000.0
    
    # Запускаем оптимизацию
    n_trials = 3
    logger.info(f"⚡ Запуск {n_trials} trials...")
    
    try:
        study.optimize(objective_with_logging, n_trials=n_trials)
        
        # Результаты
        logger.info(f"\n📊 РЕЗУЛЬТАТЫ ОПТИМИЗАЦИИ:")
        logger.info(f"   Всего trials: {len(study.trials)}")
        logger.info(f"   Завершенных: {len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])}")
        logger.info(f"   Pruned: {len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED])}")
        logger.info(f"   Ошибок: {len([t for t in study.trials if t.state == optuna.trial.TrialState.FAIL])}")
        
        if study.best_trial:
            logger.info(f"\n🏆 ЛУЧШИЙ РЕЗУЛЬТАТ:")
            logger.info(f"   Значение: {study.best_value:.6f}")
            logger.info(f"   Параметры: {study.best_params}")
            
            # Проверяем метрики лучшего trial
            if 'metrics' in study.best_trial.user_attrs:
                metrics = study.best_trial.user_attrs['metrics']
                logger.info(f"   📊 Детальные метрики:")
                logger.info(f"      Сделок: {metrics.get('total_trades', 0)}")
                logger.info(f"      Sharpe: {metrics.get('sharpe', 0):.4f}")
                logger.info(f"      Max DD: {metrics.get('max_drawdown', 0):.4f}")
                logger.info(f"      Positive days: {metrics.get('positive_days_rate', 0):.4f}")
            
            # Проверяем успешность исправления
            if study.best_value > -5:
                logger.info(f"\n🎉 ИСПРАВЛЕНИЕ РАБОТАЕТ!")
                logger.info(f"   Получен валидный результат: {study.best_value:.6f}")
                logger.info(f"   Проблема с 0 сделок решена!")
                return True
            else:
                logger.warning(f"\n⚠️ Результат все еще низкий: {study.best_value}")
                return False
        else:
            logger.error(f"\n❌ Нет лучшего trial")
            return False
            
    except Exception as e:
        logger.error(f"\n❌ Ошибка оптимизации: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Главная функция"""
    try:
        success = run_quick_optimization()
        
        if success:
            logger.info(f"\n✅ БЫСТРАЯ ОПТИМИЗАЦИЯ УСПЕШНА!")
            logger.info(f"   Можно запускать полную оптимизацию")
        else:
            logger.error(f"\n❌ ПРОБЛЕМЫ В БЫСТРОЙ ОПТИМИЗАЦИИ")
            logger.error(f"   Нужна дополнительная диагностика")
            
        return success
        
    except Exception as e:
        logger.error(f"\n💥 КРИТИЧЕСКАЯ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
