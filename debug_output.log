OMP: Info #276: omp_set_nested routine deprecated, please use omp_set_max_active_levels instead.
2025-07-30 21:43:43,486 - INFO - 🚀 Запуск оптимизации: debug_full_logs
2025-07-30 21:43:43,486 - INFO - 📊 Количество trials: 1
2025-07-30 21:43:43,486 - INFO - 💾 База данных: outputs/studies/pairs_strategy_v1.db
2025-07-30 21:43:43,487 - INFO - 🎲 Установлены глобальные сиды: 42
2025-07-30 21:43:44,222 - INFO - 🎯 Создание БЫСТРОЙ objective-функции...
2025-07-30 21:43:44,262 - INFO - 📈 Создание study...
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/coint2-Wllq0mbJ-py3.12/lib/python3.12/site-packages/optuna/_experimental.py:32: ExperimentalWarning: Argument ``multivariate`` is an experimental feature. The interface can change in the future.
  warnings.warn(
[I 2025-07-30 21:43:44,269] A new study created in RDB with name: debug_full_logs
2025-07-30 21:43:44,273 - INFO - 🔐 Хэш конфигурации: 6a6033a45b26ba1e
2025-07-30 21:43:44,293 - INFO - ⚡ Запуск оптимизации с 1 процессами...
✅ Загружено 4981 предотобранных пар

  0%|          | 0/1 [00:00<?, ?it/s]/Users/<USER>/Desktop/coint4/src/coint2/core/data_loader.py:780: PerformanceWarning: Resolving the schema of a LazyFrame is a potentially expensive operation. Use `LazyFrame.collect_schema()` to get the schema without this warning.
  print(f"📊 Схема данных: {ldf.schema}")

                                     

  0%|          | 0/1 [00:07<?, ?it/s]
100%|██████████| 1/1 [00:07<00:00,  7.60s/it]
100%|██████████| 1/1 [00:07<00:00,  7.60s/it]
2025-07-30 21:43:51,915 - ERROR - Не было завершено ни одного trial
2025-07-30 21:43:51,920 - ERROR - Оптимизация завершилась с ошибкой
📈 Загрузка данных для walk-forward шага:
   Тренировка: 2023-07-02 -> 2023-07-31
   Тестирование: 2023-08-01 -> 2023-09-30
⚙️  Загрузка данных за период: 2023-07-02 -> 2023-09-30
📂 Найдено 43 parquet файлов для обработки.
📊 Схема данных: Schema({'timestamp': Int64, 'open': Float64, 'high': Float64, 'low': Float64, 'close': Float64, 'volume': Float64, 'turnover': String, 'ts_ms': Int64, 'symbol': String})
✅ Загружено 2284610 записей с помощью Polars.
📊 Типы данных в pandas: timestamp       datetime64[us]
symbol                  object
close                  float64
timestamp_ms             int64
dtype: object
✅ Данные загружены: (8641, 276)
Trial #0: 0 сделок, Sharpe: -5.0000
Trial #0: 0 сделок, Sharpe: -5.0000
Trial #0: Недостаточно сделок (0 < 5)
[I 2025-07-30 21:43:51,897] Trial 0 pruned. Insufficient trades: 0 < 5
