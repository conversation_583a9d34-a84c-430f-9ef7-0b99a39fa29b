#!/usr/bin/env python3
"""
Тест исправления проблемы с 0 сделок в оптимизации
"""

import sys
from pathlib import Path
import optuna
import pandas as pd

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent / "src"))

from optimiser.fast_objective import FastWalkForwardObjective


class TestFastWalkForwardObjective(FastWalkForwardObjective):
    """Тестовая версия с ограниченным количеством пар"""
    
    def __init__(self, base_config_path, search_space_path):
        super().__init__(base_config_path, search_space_path)
        
        # Заменяем на тестовый файл с 10 парами
        self.preselected_pairs = pd.read_csv('outputs/test_pairs_small.csv')
        print(f"🔧 Используем тестовый файл с {len(self.preselected_pairs)} парами")


def test_optimization_fix():
    """Тест исправления оптимизации с ограниченными данными"""
    
    print("🔍 ТЕСТ ИСПРАВЛЕНИЯ ОПТИМИЗАЦИИ")
    print("="*40)
    
    # Создаем тестовую objective
    objective = TestFastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    
    print(f"✅ Тестовая objective создана")
    print(f"   Пар для тестирования: {len(objective.preselected_pairs)}")
    
    # Тест 1: Прямой вызов с параметрами
    print(f"\n🔧 ТЕСТ 1: Прямой вызов с dict параметрами")
    test_params = {
        'zscore_threshold': 1.0,
        'zscore_exit': 0.3,
        'rolling_window': 25,
        'max_active_positions': 15,
        'risk_per_position_pct': 0.02,
        'max_position_size_pct': 0.1,
        'stop_loss_multiplier': 3.0,
        'time_stop_multiplier': 5.0,
        'cooldown_hours': 2,
        'commission_pct': 0.0004,
        'slippage_pct': 0.0005,
        'normalization_method': 'minmax',
        'min_history_ratio': 0.6,
        'trial_number': -1
    }
    
    try:
        result_dict = objective(test_params)
        print(f"   ✅ Результат с dict: {result_dict}")
        dict_success = result_dict > -5
    except Exception as e:
        print(f"   ❌ Ошибка с dict: {e}")
        dict_success = False
    
    # Тест 2: Вызов с trial объектом
    print(f"\n🔧 ТЕСТ 2: Вызов с trial объектом")
    study = optuna.create_study(direction="maximize")
    trial = study.ask()
    
    try:
        result_trial = objective(trial)
        print(f"   ✅ Результат с trial: {result_trial}")
        trial_success = result_trial > -5
    except Exception as e:
        print(f"   ❌ Ошибка с trial: {e}")
        trial_success = False
        import traceback
        traceback.print_exc()
    
    # Тест 3: Минимальная оптимизация
    print(f"\n🔧 ТЕСТ 3: Минимальная оптимизация")
    
    def test_objective_func(trial):
        try:
            result = objective(trial)
            print(f"      Trial {trial.number}: {result}")
            return result
        except Exception as e:
            print(f"      Trial {trial.number} ошибка: {e}")
            return -1000.0
    
    try:
        study.optimize(test_objective_func, n_trials=1)
        optimization_success = len(study.trials) > 0 and study.trials[0].value > -5
        print(f"   ✅ Оптимизация завершена: {study.trials[0].value if study.trials else 'N/A'}")
    except Exception as e:
        print(f"   ❌ Ошибка оптимизации: {e}")
        optimization_success = False
    
    # Итоги
    print(f"\n🎯 ИТОГИ ТЕСТИРОВАНИЯ:")
    print(f"   Dict параметры: {'✅' if dict_success else '❌'}")
    print(f"   Trial объект: {'✅' if trial_success else '❌'}")
    print(f"   Оптимизация: {'✅' if optimization_success else '❌'}")
    
    if dict_success and trial_success and optimization_success:
        print(f"\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ! ПРОБЛЕМА РЕШЕНА!")
        print(f"   Исправление несоответствия ключей работает")
        print(f"   Оптимизация теперь работает корректно")
        return True
    else:
        print(f"\n❌ ЕСТЬ ПРОБЛЕМЫ:")
        if not dict_success:
            print(f"   - Проблема с dict параметрами")
        if not trial_success:
            print(f"   - Проблема с trial объектом")
        if not optimization_success:
            print(f"   - Проблема с оптимизацией")
        return False


def main():
    """Главная функция"""
    try:
        success = test_optimization_fix()
        return success
    except Exception as e:
        print(f"\n❌ КРИТИЧЕСКАЯ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
