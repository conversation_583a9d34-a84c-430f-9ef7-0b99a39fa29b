#!/usr/bin/env python3

print("🔍 ПРОСТОЙ ТЕСТ")
print("Python работает!")

import sys
print(f"Python версия: {sys.version}")

sys.path.append('src')
print("Путь добавлен")

try:
    from optimiser.fast_objective import FastWalkForwardObjective
    print("✅ Импорт успешен")
    
    # Создаем objective
    obj = FastWalkForwardObjective('configs/main_2024.yaml', 'configs/search_space_fast.yaml')
    print(f"✅ Objective создан, пар: {len(obj.preselected_pairs)}")
    
except Exception as e:
    print(f"❌ Ошибка: {e}")
    import traceback
    traceback.print_exc()

print("🎯 ТЕСТ ЗАВЕРШЕН")
